import { v4 as uuidv4 } from 'uuid';
import { GPSMessage, Voyage } from './types.js';

// In-memory storage for GPS messages and voyages
const gpsMessages = new Map<string, GPSMessage>();
const voyages = new Map<string, Voyage>();
const activeVoyages = new Map<string, string>(); // imei -> voyageId
const trackerLastSeen = new Map<string, Date>(); // imei -> last message timestamp

export class GPSHandler {
  
  // Process incoming GPS data from trackers
  processGPSData(rawData: any): GPSMessage | null {
    try {
      // This is a placeholder for GPS message parsing
      // The actual implementation will depend on the GPS tracker protocol
      const message: GPSMessage = {
        id: uuidv4(),
        imei: rawData.imei || 'unknown',
        trackerTimestamp: new Date(rawData.timestamp || Date.now()),
        serverTimestamp: new Date(),
        latitude: parseFloat(rawData.lat || 0),
        longitude: parseFloat(rawData.lng || 0),
        speed: parseFloat(rawData.speed || 0),
        status: this.parseStatus(rawData.status),
        batteryLevel: parseInt(rawData.battery || 100),
      };

      // Handle voyage tracking
      this.handleVoyageTracking(message);

      // Check if this is a new connection (first message in 5+ minutes)
      const wasConnected = this.isTrackerConnected(message.imei);

      // Update tracker last seen timestamp
      trackerLastSeen.set(message.imei, message.serverTimestamp);

      // Check if connection status changed
      const isNowConnected = this.isTrackerConnected(message.imei);
      const statusChanged = wasConnected !== isNowConnected;

      // Store the message
      gpsMessages.set(message.id, message);

      // Notify about status change if needed
      if (statusChanged) {
        console.log(`📡 Tracker ${message.imei} connection status changed: ${wasConnected ? 'connected' : 'disconnected'} -> ${isNowConnected ? 'connected' : 'disconnected'}`);
        this.notifyTrackerStatusChange(message.imei, isNowConnected);
      }

      return message;
    } catch (error) {
      console.error('Error processing GPS data:', error);
      return null;
    }
  }

  private parseStatus(status: any): 'Inizio' | 'Fine' | 'GPS Fixed' | 'No GPS Fixed' {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'trip_start':
        case 'start':
        case 'inizio':
          return 'Inizio';
        case 'trip_end':
        case 'end':
        case 'fine':
          return 'Fine';
        case 'fixed':
        case 'gps_fixed':
          return 'GPS Fixed';
        case 'no_fixed':
        case 'no_fix':
        case 'no_gps_fixed':
          return 'No GPS Fixed';
        default:
          return 'GPS Fixed';
      }
    }
    return 'GPS Fixed';
  }

  private handleVoyageTracking(message: GPSMessage): void {
    const { imei, status } = message;
    console.log(`🚢 Voyage tracking for IMEI ${imei}, status: ${status}`);

    if (status === 'Inizio') {
      // Start a new voyage
      const voyageId = uuidv4();
      const voyage: Voyage = {
        id: voyageId,
        imei,
        startTime: message.trackerTimestamp,
        messages: [message]
      };

      voyages.set(voyageId, voyage);
      activeVoyages.set(imei, voyageId);
      message.voyageId = voyageId;

      console.log(`✅ New voyage created: ${voyageId} for IMEI ${imei}`);
      console.log(`📊 Total voyages: ${voyages.size}, Active voyages: ${activeVoyages.size}`);

    } else if (status === 'Fine') {
      // End current voyage
      const voyageId = activeVoyages.get(imei);
      console.log(`🏁 Ending voyage for IMEI ${imei}, voyageId: ${voyageId}`);

      if (voyageId) {
        const voyage = voyages.get(voyageId);
        if (voyage) {
          voyage.endTime = message.trackerTimestamp;
          voyage.messages.push(message);
          message.voyageId = voyageId;
          console.log(`✅ Voyage ${voyageId} ended with ${voyage.messages.length} messages`);
        }
        activeVoyages.delete(imei);
        console.log(`📊 Active voyages remaining: ${activeVoyages.size}`);
      } else {
        console.log(`❌ No active voyage found for IMEI ${imei} to end`);
      }

    } else {
      // Add to current voyage if exists
      const voyageId = activeVoyages.get(imei);
      if (voyageId) {
        const voyage = voyages.get(voyageId);
        if (voyage) {
          voyage.messages.push(message);
          message.voyageId = voyageId;
          console.log(`📍 Added message to voyage ${voyageId}, total messages: ${voyage.messages.length}`);
        }
      } else {
        console.log(`⚠️ No active voyage for IMEI ${imei}, message not added to voyage`);
      }
    }
  }

  // Get messages for specific IMEI codes
  getMessagesForImei(imeiCodes: string[]): GPSMessage[] {
    const messages: GPSMessage[] = [];
    
    for (const message of gpsMessages.values()) {
      if (imeiCodes.includes(message.imei)) {
        messages.push(message);
      }
    }
    
    return messages.sort((a, b) => b.serverTimestamp.getTime() - a.serverTimestamp.getTime());
  }

  // Get voyages for specific IMEI codes
  getVoyagesForImei(imeiCodes: string[]): Voyage[] {
    console.log(`🔍 Getting voyages for IMEIs: ${imeiCodes.join(', ')}`);
    console.log(`📊 Total voyages in memory: ${voyages.size}`);

    const userVoyages: Voyage[] = [];

    for (const voyage of voyages.values()) {
      console.log(`   Checking voyage ${voyage.id} for IMEI ${voyage.imei}`);
      if (imeiCodes.includes(voyage.imei)) {
        userVoyages.push(voyage);
        console.log(`   ✅ Added voyage ${voyage.id} (${voyage.messages.length} messages)`);
      }
    }

    console.log(`📋 Returning ${userVoyages.length} voyages for user`);
    return userVoyages.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  // Get messages for a specific voyage
  getVoyageMessages(voyageId: string): GPSMessage[] {
    const voyage = voyages.get(voyageId);
    return voyage ? voyage.messages : [];
  }

  // Get all messages (for admin purposes)
  getAllMessages(): GPSMessage[] {
    return Array.from(gpsMessages.values())
      .sort((a, b) => b.serverTimestamp.getTime() - a.serverTimestamp.getTime());
  }

  // Get all voyages (for admin purposes)
  getAllVoyages(): Voyage[] {
    return Array.from(voyages.values())
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  // Check if a tracker is connected (received data in last 30 seconds for testing)
  isTrackerConnected(imei: string): boolean {
    const lastSeen = trackerLastSeen.get(imei);
    if (!lastSeen) return false;

    // Temporarily reduced to 30 seconds for testing
    const thirtySecondsAgo = new Date(Date.now() - 30 * 1000);
    return lastSeen > thirtySecondsAgo;
  }

  // Get tracker connection status for all IMEIs
  getTrackerConnectionStatus(imeiCodes: string[]): { [imei: string]: boolean } {
    const status: { [imei: string]: boolean } = {};
    imeiCodes.forEach(imei => {
      status[imei] = this.isTrackerConnected(imei);
    });
    return status;
  }

  // Callback for tracker status changes
  private statusChangeCallback: ((imei: string, isConnected: boolean) => void) | null = null;

  // Set callback for tracker status changes
  setTrackerStatusChangeCallback(callback: (imei: string, isConnected: boolean) => void): void {
    this.statusChangeCallback = callback;
  }

  // Notify about tracker status change
  private notifyTrackerStatusChange(imei: string, isConnected: boolean): void {
    if (this.statusChangeCallback) {
      this.statusChangeCallback(imei, isConnected);
    }
  }

  // Check for disconnected trackers (call this periodically)
  checkForDisconnectedTrackers(): string[] {
    const disconnectedTrackers: string[] = [];
    const thirtySecondsAgo = new Date(Date.now() - 30 * 1000);

    for (const [imei, lastSeen] of trackerLastSeen.entries()) {
      const wasConnected = lastSeen > thirtySecondsAgo;
      if (!wasConnected) {
        // Check if we need to notify about disconnection
        const currentTime = new Date();
        const timeSinceLastSeen = currentTime.getTime() - lastSeen.getTime();

        // Only notify once when it just became disconnected (between 30-60 seconds)
        if (timeSinceLastSeen >= 30 * 1000 && timeSinceLastSeen < 60 * 1000) {
          disconnectedTrackers.push(imei);
          console.log(`📡 Tracker ${imei} detected as disconnected (last seen: ${lastSeen.toISOString()})`);
          this.notifyTrackerStatusChange(imei, false);
        }
      }
    }

    return disconnectedTrackers;
  }

  // Debug method to get all data
  getDebugInfo(): any {
    return {
      totalMessages: gpsMessages.size,
      totalVoyages: voyages.size,
      activeVoyages: activeVoyages.size,
      voyagesList: Array.from(voyages.values()).map(v => ({
        id: v.id,
        imei: v.imei,
        startTime: v.startTime,
        endTime: v.endTime,
        messageCount: v.messages.length
      })),
      messagesList: Array.from(gpsMessages.values()).map(m => ({
        id: m.id,
        imei: m.imei,
        status: m.status,
        voyageId: m.voyageId,
        timestamp: m.trackerTimestamp
      }))
    };
  }

  // Get last seen timestamp for a tracker
  getTrackerLastSeen(imei: string): Date | null {
    return trackerLastSeen.get(imei) || null;
  }
}
