import net from 'net';

console.log('🛰️ Simulatore GPS per IMEI 9876543210');

const client = new net.Socket();

client.connect(8090, 'localhost', () => {
  console.log('✅ Connesso al server GPS');
  
  // Invia alcuni messaggi GPS per IMEI 9876543210
  const messages = [
    {
      imei: '9876543210',
      timestamp: new Date().toISOString(),
      lat: 45.4642,
      lng: 9.1900,
      speed: 0,
      status: 'Inizio',
      battery: 95
    },
    {
      imei: '9876543210',
      timestamp: new Date(Date.now() + 3000).toISOString(),
      lat: 45.4700,
      lng: 9.1950,
      speed: 50,
      status: 'GPS Fixed',
      battery: 93
    },
    {
      imei: '9876543210',
      timestamp: new Date(Date.now() + 6000).toISOString(),
      lat: 45.4800,
      lng: 9.2000,
      speed: 0,
      status: 'Fine',
      battery: 91
    }
  ];
  
  let messageIndex = 0;
  
  const sendMessage = () => {
    if (messageIndex < messages.length) {
      const message = messages[messageIndex];
      console.log(`📡 Invio messaggio ${messageIndex + 1}/${messages.length}:`, message.status);
      client.write(JSON.stringify(message) + '\n');
      messageIndex++;
      setTimeout(sendMessage, 3000);
    } else {
      console.log('✅ Tutti i messaggi inviati. Il tracker si disconnetterà ora...');
      setTimeout(() => {
        client.destroy();
        console.log('❌ Connessione chiusa - Tracker disconnesso');
        process.exit(0);
      }, 1000);
    }
  };
  
  // Inizia a inviare messaggi dopo 1 secondo
  setTimeout(sendMessage, 1000);
});

client.on('data', (data) => {
  console.log('📥 Risposta server:', data.toString());
});

client.on('close', () => {
  console.log('❌ Connessione al server chiusa');
});

client.on('error', (err) => {
  console.error('🚨 Errore connessione:', err.message);
  process.exit(1);
});
