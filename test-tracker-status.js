import WebSocket from 'ws';

// Test per verificare lo stato del tracker
console.log('🧪 Test stato tracker - Connessione e disconnessione');

// Connessione al server WebSocket
const ws = new WebSocket('ws://localhost:8090/ws');

ws.on('open', () => {
  console.log('✅ Connesso al server WebSocket');
  
  // Autenticazione
  ws.send(JSON.stringify({
    type: 'auth',
    data: {
      username: 'testuser',
      password: 'password123'
    }
  }));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📨 Messaggio ricevuto:', message.type);
    
    if (message.type === 'auth' && message.data?.success) {
      console.log('✅ Autenticazione riuscita!');
      
      // Richiedi stato tracker
      console.log('📡 Richiedo stato tracker...');
      ws.send(JSON.stringify({
        type: 'tracker_status'
      }));
    }
    
    if (message.type === 'tracker_status') {
      console.log('📊 Stato tracker ricevuto:', message.data);
      
      // Mostra stato per ogni IMEI
      Object.entries(message.data).forEach(([imei, isConnected]) => {
        console.log(`   IMEI ${imei}: ${isConnected ? '🟢 CONNESSO' : '🔴 DISCONNESSO'}`);
      });
    }
    
    if (message.type === 'gps_data') {
      console.log(`📍 Nuovo messaggio GPS da IMEI ${message.data.imei}: ${message.data.status}`);
    }
    
  } catch (error) {
    console.log('📥 Messaggio raw:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ Connessione WebSocket chiusa');
  process.exit(0);
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});

// Richiedi stato ogni 10 secondi
setInterval(() => {
  if (ws.readyState === WebSocket.OPEN) {
    console.log('🔄 Richiedo aggiornamento stato tracker...');
    ws.send(JSON.stringify({
      type: 'tracker_status'
    }));
  }
}, 10000);
